-- Project Polygon Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS polygon;
USE polygon;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) DEFAULT 'placeholder',
    jointime INT NOT NULL,
    lastonline INT NOT NULL,
    regip VARCHAR(45) NOT NULL,
    status TEXT DEFAULT 'I\'m new to Polygon!',
    currency INT DEFAULT 0,
    nextCurrencyStipend INT DEFAULT 0,
    adminlevel INT DEFAULT 0,
    filter BOOLEAN DEFAULT 1,
    pageanim BOOLEAN DEFAULT 1,
    lastadminaction INT DEFAULT 0
);

-- Sessions table
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sessionKey VARCHAR(256) NOT NULL UNIQUE,
    userAgent TEXT NOT NULL,
    userId INT NOT NULL,
    loginIp VARCHAR(45) NOT NULL,
    created INT NOT NULL,
    lastonline INT NOT NULL,
    csrf VARCHAR(64) NOT NULL,
    valid BOOLEAN DEFAULT 1,
    FOREIGN KEY (userId) REFERENCES users(id)
);

-- Blacklisted names table
CREATE TABLE blacklistednames (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    exact BOOLEAN DEFAULT 0
);

-- Friends table
CREATE TABLE friends (
    id INT AUTO_INCREMENT PRIMARY KEY,
    requesterId INT NOT NULL,
    receiverId INT NOT NULL,
    status INT DEFAULT 0, -- 0=pending, 1=accepted, 2=declined
    FOREIGN KEY (requesterId) REFERENCES users(id),
    FOREIGN KEY (receiverId) REFERENCES users(id)
);

-- Bans table
CREATE TABLE bans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userId INT NOT NULL,
    bannerId INT NOT NULL,
    timeStarted INT NOT NULL,
    timeEnds INT DEFAULT 0,
    reason TEXT NOT NULL,
    banType INT NOT NULL, -- 1=warning, 2=ban, 3=permanent ban, 4=unban
    note TEXT,
    isDismissed BOOLEAN DEFAULT 0,
    FOREIGN KEY (userId) REFERENCES users(id),
    FOREIGN KEY (bannerId) REFERENCES users(id)
);

-- Staff logs table
CREATE TABLE stafflogs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    time INT NOT NULL,
    adminId INT NOT NULL,
    action TEXT NOT NULL,
    FOREIGN KEY (adminId) REFERENCES users(id)
);

-- Forum forums table
CREATE TABLE forum_forums (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    displayposition INT DEFAULT 0
);

-- Forum subforums table
CREATE TABLE forum_subforums (
    id INT AUTO_INCREMENT PRIMARY KEY,
    forumid INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    displayposition INT DEFAULT 0,
    FOREIGN KEY (forumid) REFERENCES forum_forums(id)
);

-- Forum threads table
CREATE TABLE forum_threads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subforumid INT NOT NULL,
    author INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    created INT NOT NULL,
    bumpIndex INT NOT NULL,
    pinned BOOLEAN DEFAULT 0,
    deleted BOOLEAN DEFAULT 0,
    FOREIGN KEY (subforumid) REFERENCES forum_subforums(id),
    FOREIGN KEY (author) REFERENCES users(id)
);

-- Forum replies table
CREATE TABLE forum_replies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    threadId INT NOT NULL,
    author INT NOT NULL,
    body TEXT NOT NULL,
    created INT NOT NULL,
    deleted BOOLEAN DEFAULT 0,
    FOREIGN KEY (threadId) REFERENCES forum_threads(id),
    FOREIGN KEY (author) REFERENCES users(id)
);

-- Insert some sample data
INSERT INTO blacklistednames (username, exact) VALUES 
('admin', 1),
('administrator', 1),
('moderator', 1),
('mod', 1);

-- Insert sample forum structure
INSERT INTO forum_forums (name, description, displayposition) VALUES 
('General Discussion', 'Talk about anything here!', 1),
('Announcements', 'Official announcements and news', 0);

INSERT INTO forum_subforums (forumid, name, description, displayposition) VALUES
(1, 'General Chat', 'General discussion about anything', 1),
(1, 'Introductions', 'Introduce yourself to the community', 2),
(2, 'Site News', 'Latest updates and announcements', 1);

-- Announcements table (missing from original schema)
CREATE TABLE announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    text TEXT NOT NULL,
    bgcolor VARCHAR(50) DEFAULT '#007bff',
    textcolor VARCHAR(50) DEFAULT 'alert-primary',
    activated BOOLEAN DEFAULT 1,
    created INT NOT NULL DEFAULT (UNIX_TIMESTAMP())
);

-- Insert sample announcements
INSERT INTO announcements (text, bgcolor, textcolor, activated, created) VALUES
('🎉 Welcome to Project Polygon! This is a sample announcement.', '#28a745', 'alert-success', 1, UNIX_TIMESTAMP()),
('📢 Check out the forum for discussions and updates!', '#17a2b8', 'alert-info', 1, UNIX_TIMESTAMP());
