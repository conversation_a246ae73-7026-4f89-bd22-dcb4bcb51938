-- Add missing announcements table to Project Polygon database
USE polygon;

-- Create announcements table
CREATE TABLE announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    text TEXT NOT NULL,
    bgcolor VARCHAR(50) DEFAULT '#007bff',
    textcolor VARCHAR(50) DEFAULT 'alert-primary',
    activated B<PERSON>OLEAN DEFAULT 1,
    created INT NOT NULL DEFAULT (UNIX_TIMESTAMP())
);

-- Insert sample announcement
INSERT INTO announcements (text, bgcolor, textcolor, activated, created) VALUES 
('🎉 Welcome to Project Polygon! This is a sample announcement.', '#28a745', 'alert-success', 1, UNIX_TIMESTAMP()),
('📢 Check out the forum for discussions and updates!', '#17a2b8', 'alert-info', 1, UNIX_TIMESTAMP());
